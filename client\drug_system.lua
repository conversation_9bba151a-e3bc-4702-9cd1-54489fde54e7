-- 迷药系统客户端

local isDrugged = false
local drugThread = nil
local screenEffectThread = nil
local isCarrying = false
local carriedPlayer = nil
local carryType = nil

-- 应用迷药效果
RegisterNetEvent('organ_trade:applyDrugEffect', function(duration)
    if isDrugged then return end

    isDrugged = true
    local playerPed = PlayerPedId()

    -- 通知玩家被下药
    lib.notify({
        title = '器官交易系统',
        description = '你感到头晕目眩...',
        type = 'error',
        duration = 5000
    })

    -- 开始倒地效果
    StartKnockdownEffect()

    -- 开始屏幕效果
    StartScreenEffects()

    -- 设置效果持续时间
    CreateThread(function()
        Wait(duration)
        RemoveDrugEffect()
    end)
end)

-- 移除迷药效果
RegisterNetEvent('organ_trade:removeDrugEffect', function()
    RemoveDrugEffect()
end)

-- 移除迷药效果函数
function RemoveDrugEffect()
    if not isDrugged then return end

    isDrugged = false

    -- 停止倒地效果
    StopKnockdownEffect()

    -- 停止屏幕效果
    StopScreenEffects()

    lib.notify({
        title = '器官交易系统',
        description = '药效已消退...',
        type = 'info'
    })
end

-- 开始倒地效果
function StartKnockdownEffect()
    local playerPed = PlayerPedId()

    -- 主要使用受伤扭动动画
    RequestAnimDict("combat@damage@writhe")
    while not HasAnimDictLoaded("combat@damage@writhe") do
        Wait(100)
    end

    -- 让玩家倒地并扭动（受伤状态）
    TaskPlayAnim(playerPed, "combat@damage@writhe", "writhe_loop", 8.0, -8.0, -1, 1, 0, false, false, false)

    

    -- 创建持续效果线程
    drugThread = CreateThread(function()
        while isDrugged do
            local playerPed = PlayerPedId()

            -- 禁用玩家控制
            DisableControlAction(0, 21, true) -- 冲刺
            DisableControlAction(0, 22, true) -- 跳跃
            DisableControlAction(0, 23, true) -- 进入载具
            DisableControlAction(0, 75, true) -- 离开载具
            DisableControlAction(0, 36, true) -- 蹲下
            DisableControlAction(0, 44, true) -- 掩护

            -- 如果玩家试图站起来，强制让他们保持受伤扭动状态
            if not IsEntityPlayingAnim(playerPed, "combat@damage@writhe", "writhe_loop", 3) and
               not IsEntityPlayingAnim(playerPed, "combat@damage@injured_pistol", "injured_pistol_loop", 3) then

                -- 优先播放受伤扭动动画
                if HasAnimDictLoaded("combat@damage@writhe") then
                    TaskPlayAnim(playerPed, "combat@damage@writhe", "writhe_loop", 8.0, -8.0, -1, 1, 0, false, false, false)
                elseif HasAnimDictLoaded("combat@damage@injured_pistol") then
                    TaskPlayAnim(playerPed, "combat@damage@injured_pistol", "injured_pistol_loop", 8.0, -8.0, -1, 1, 0, false, false, false)
                end
            end

            -- 设置玩家生命值不会降到0以下（防止真正死亡）
            local health = GetEntityHealth(playerPed)
            if health < 200 then
                SetEntityHealth(playerPed, 200)
            end

            Wait(100)
        end
    end)

    -- 额外的通知
    CreateThread(function()
        Wait(2000)
        if isDrugged then
            lib.notify({
                title = '器官交易系统',
                description = '你无法站起来...',
                type = 'error',
                duration = 3000
            })
        end
    end)
end

-- 停止倒地效果
function StopKnockdownEffect()
    if drugThread then
        drugThread = nil
    end

    local playerPed = PlayerPedId()

    -- 停止所有受伤扭动动画
    StopAnimTask(playerPed, "combat@damage@writhe", "writhe_loop", 3.0)
    StopAnimTask(playerPed, "combat@damage@injured_pistol", "injured_pistol_loop", 3.0)

    -- 清除所有任务让玩家站起来
    ClearPedTasks(playerPed)
    ClearPedTasksImmediately(playerPed)

    -- 播放站起来的动画
    RequestAnimDict("get_up@directional@movement@from_knees@action")
    CreateThread(function()
        while not HasAnimDictLoaded("get_up@directional@movement@from_knees@action") do
            Wait(100)
        end
        TaskPlayAnim(playerPed, "get_up@directional@movement@from_knees@action", "getup_r_0", 8.0, -8.0, 3000, 0, 0, false, false, false)

        -- 清理动画字典
        Wait(3000)
        RemoveAnimDict("combat@damage@writhe")
        RemoveAnimDict("combat@damage@injured_pistol")
        RemoveAnimDict("get_up@directional@movement@from_knees@action")
    end)
end

-- 传送玩家
RegisterNetEvent('organ_trade:teleportPlayer', function(coords)
    if not isDrugged then return end

    local playerPed = PlayerPedId()
    SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)

    lib.notify({
        title = '器官交易系统',
        description = '你被带到了一个陌生的地方...',
        type = 'error'
    })
end)

-- 搬运被迷晕玩家的交互功能
function ShowCarryMenu()
    -- 检查自己是否被迷晕
    if isDrugged then
        lib.notify({
            title = '器官交易系统',
            description = '你处于迷晕状态，无法搬运他人',
            type = 'error'
        })
        return
    end

    -- 获取附近被迷晕的玩家
    TriggerServerEvent('organ_trade:getNearbyDruggedPlayersForCarry')
end

-- 接收附近被迷晕的玩家（用于搬运）
RegisterNetEvent('organ_trade:receiveNearbyDruggedPlayersForCarry', function(players)
    if #players == 0 then
        lib.notify({
            title = '器官交易系统',
            description = '附近没有被迷晕的玩家可以搬运',
            type = 'error'
        })
        return
    end

    -- 创建搬运选择菜单
    local options = {}

    for _, player in ipairs(players) do
        table.insert(options, {
            title = string.format('%s (距离: %.1fm)', player.name, player.distance),
            description = '选择搬运方式',
            onSelect = function()
                ShowCarryOptionsMenu(player.id, player.name)
            end
        })
    end

    lib.registerContext({
        id = 'carry_target_menu',
        title = '选择搬运目标',
        options = options
    })

    lib.showContext('carry_target_menu')
end)

-- 显示搬运确认菜单
function ShowCarryOptionsMenu(targetId, targetName)
    local alert = lib.alertDialog({
        header = '确认背负',
        content = string.format('确认背负 %s？', targetName),
        centered = true,
        cancel = true
    })

    if alert == 'confirm' then
        TriggerServerEvent('organ_trade:startCarrying', targetId, 'carry')
    end
end



-- 开始迷药选择流程
RegisterNetEvent('organ_trade:startDrugSelection', function()
    UseDrugItem()
end)

-- 使用迷药道具
function UseDrugItem()
    -- 检查自己是否被迷晕
    if isDrugged then
        lib.notify({
            title = '器官交易系统',
            description = Config.Notifications.user_drugged_cannot_use,
            type = 'error'
        })
        return
    end

    -- 获取附近玩家
    TriggerServerEvent('organ_trade:getNearbyPlayers')
end

-- 接收附近玩家列表
RegisterNetEvent('organ_trade:receiveNearbyPlayers', function(players)
    if #players == 0 then
        lib.notify({
            title = '器官交易系统',
            description = '附近没有玩家',
            type = 'error'
        })
        return
    end

    -- 创建玩家选择菜单
    local options = {}

    for _, player in ipairs(players) do
        local title = string.format('%s (距离: %.1fm)', player.name, player.distance)
        local description = '选择此玩家使用迷药'

        -- 如果玩家已被迷晕，修改显示和描述
        if player.isDrugged then
            title = title .. ' [已被迷晕]'
            description = '该玩家已被迷晕，无法重复使用迷药'
        end

        table.insert(options, {
            title = title,
            description = description,
            disabled = player.isDrugged, -- 禁用已被迷晕的玩家选项
            onSelect = function()
                if player.isDrugged then
                    lib.notify({
                        title = '器官交易系统',
                        description = '该玩家已被迷晕，无法重复使用迷药',
                        type = 'error'
                    })
                    return
                end

                -- 确认对话框
                local alert = lib.alertDialog({
                    header = '确认使用迷药',
                    content = '确认对此玩家使用迷药？',
                    centered = true,
                    cancel = true
                })

                if alert == 'confirm' then
                    TriggerServerEvent('organ_trade:useDrug', player.id)
                end
            end
        })
    end

    lib.registerContext({
        id = 'drug_target_menu',
        title = '选择目标',
        options = options
    })

    lib.showContext('drug_target_menu')
end)

-- 检查迷药状态
function CheckDrugStatus()
    TriggerServerEvent('organ_trade:checkDrugStatus')
end

-- 接收迷药状态响应
RegisterNetEvent('organ_trade:drugStatusResponse', function(drugged)
    isDrugged = drugged
end)

-- 玩家生成时检查状态
AddEventHandler('playerSpawned', function()
    CreateThread(function()
        Wait(2000) -- 等待ESX加载
        CheckDrugStatus()
    end)
end)

-- 开始屏幕效果
function StartScreenEffects()
    -- 应用屏幕模糊效果
    SetTimecycleModifier('spectator5')
    SetTimecycleModifierStrength(0.8)

    -- 开始屏幕晃动和视觉效果线程
    screenEffectThread = CreateThread(function()
        local shakeIntensity = 0.5
        local blurStrength = 0.8
        local effectTimer = 0

        while isDrugged do
            -- 屏幕晃动效果
            ShakeGameplayCam('DRUNK_SHAKE', shakeIntensity)

            -- 动态调整效果强度
            effectTimer = effectTimer + 1

            -- 每5秒变化一次强度，模拟药效波动
            if effectTimer % 500 == 0 then
                shakeIntensity = math.random(30, 80) / 100  -- 0.3-0.8之间随机
                blurStrength = math.random(60, 100) / 100   -- 0.6-1.0之间随机
                SetTimecycleModifierStrength(blurStrength)
            end

            -- 添加随机的强烈晃动
            if math.random(1, 200) == 1 then
                ShakeGameplayCam('VIBRATE_SHAKE', 1.5)
                Wait(500)
            end

            -- 模拟视觉扭曲
            if math.random(1, 300) == 1 then
                SetTimecycleModifier('drug_flying_base')
                Wait(2000)
                if isDrugged then
                    SetTimecycleModifier('spectator5')
                end
            end

            Wait(10)
        end
    end)
end

-- 停止屏幕效果
function StopScreenEffects()
    if screenEffectThread then
        screenEffectThread = nil
    end

    -- 停止摄像头晃动
    StopGameplayCamShaking(true)

    -- 清除时间周期修改器
    ClearTimecycleModifier()

    -- 渐进式恢复正常视觉
    CreateThread(function()
        local strength = 0.8
        while strength > 0 do
            SetTimecycleModifier('spectator5')
            SetTimecycleModifierStrength(strength)
            strength = strength - 0.05
            Wait(100)
        end
        ClearTimecycleModifier()
    end)
end

-- 开始搬运
RegisterNetEvent('organ_trade:startCarryingAnimation', function(targetId)
    if isCarrying then
        lib.notify({
            title = '器官交易系统',
            description = '你已经在搬运其他人了',
            type = 'error'
        })
        return
    end

    isCarrying = true
    carriedPlayer = targetId
    carryType = 'carry'

    local playerPed = PlayerPedId()
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetId))

    StartCarryAnimation(playerPed, targetPed)

    lib.notify({
        title = '器官交易系统',
        description = '开始扛肩，按 [E] 停止',
        type = 'info'
    })
end)

-- 停止搬运
RegisterNetEvent('organ_trade:stopCarrying', function()
    if not isCarrying then return end

    StopCarryingAnimation()

    lib.notify({
        title = '器官交易系统',
        description = '停止扛肩',
        type = 'info'
    })
end)

-- 被扛玩家接收扛肩状态
RegisterNetEvent('organ_trade:beingCarried', function(carrierId)
    if not isDrugged then return end

    lib.notify({
        title = '器官交易系统',
        description = '你被扛在肩上...',
        type = 'error'
    })
end)

-- 被扛玩家停止被扛状态
RegisterNetEvent('organ_trade:stopBeingCarried', function()
    if not isDrugged then return end

    lib.notify({
        title = '器官交易系统',
        description = '你被放下了...',
        type = 'info'
    })
end)

-- 扛肩动画
function StartCarryAnimation(playerPed, targetPed)
    -- 加载扛肩动画字典
    RequestAnimDict("missfinale_c2mcs_1")
    while not HasAnimDictLoaded("missfinale_c2mcs_1") do
        Wait(100)
    end

    -- 播放扛肩者动画
    TaskPlayAnim(playerPed, "missfinale_c2mcs_1", "fin_c2_mcs_1_camman", 8.0, -8.0, -1, 49, 0, false, false, false)

    -- 如果目标玩家存在，将其附加到扛肩者身上
    if targetPed and DoesEntityExist(targetPed) then
        -- 将被扛玩家附加到扛肩者的肩膀上
        AttachEntityToEntity(
            targetPed,           -- 被附加的实体（被扛的玩家）
            playerPed,           -- 附加到的实体（扛肩者）
            GetPedBoneIndex(playerPed, 24816), -- 肩膀骨骼索引
            0.3,                 -- X偏移
            0.15,                -- Y偏移
            0.63,                -- Z偏移
            0.0,                 -- X旋转
            0.0,                 -- Y旋转
            0.0,                 -- Z旋转
            false,               -- 物理
            false,               -- 使用软固定
            false,               -- 碰撞
            false,               -- 是否为玩家
            2,                   -- 旋转顺序
            true                 -- 固定旋转
        )

        -- 让被扛玩家播放被扛动画
        RequestAnimDict("nm")
        while not HasAnimDictLoaded("nm") do
            Wait(100)
        end
        TaskPlayAnim(targetPed, "nm", "firemans_carry", 8.0, -8.0, -1, 33, 0, false, false, false)
    end

    -- 创建搬运控制线程
    CreateThread(function()
        while isCarrying do
            -- 检查停止按键
            if IsControlJustPressed(0, 38) then -- E键
                TriggerServerEvent('organ_trade:stopCarrying', carriedPlayer)
                break
            end

            -- 显示帮助文本
            SetTextComponentFormat('STRING')
            AddTextComponentString('按 [E] 停止扛肩')
            DisplayHelpTextFromStringLabel(0, 0, 1, -1)

            -- 确保动画持续播放
            if not IsEntityPlayingAnim(playerPed, "missfinale_c2mcs_1", "fin_c2_mcs_1_camman", 3) then
                TaskPlayAnim(playerPed, "missfinale_c2mcs_1", "fin_c2_mcs_1_camman", 8.0, -8.0, -1, 49, 0, false, false, false)
            end

            Wait(0)
        end
    end)
end

-- 停止搬运动画
function StopCarryingAnimation()
    local playerPed = PlayerPedId()

    -- 如果有被扛的玩家，先分离
    if carriedPlayer then
        local targetPed = GetPlayerPed(GetPlayerFromServerId(carriedPlayer))
        if targetPed and DoesEntityExist(targetPed) then
            -- 分离被扛的玩家
            DetachEntity(targetPed, true, true)

            -- 清除被扛玩家的动画
            ClearPedTasks(targetPed)

            -- 让被扛玩家恢复迷晕状态的动画
            if HasAnimDictLoaded("combat@damage@writhe") then
                TaskPlayAnim(targetPed, "combat@damage@writhe", "writhe_loop", 8.0, -8.0, -1, 1, 0, false, false, false)
            end
        end
    end

    -- 清除扛肩者动画
    ClearPedTasks(playerPed)

    -- 重置状态
    isCarrying = false
    carriedPlayer = nil
    carryType = nil
end

-- 搬运按键检测
CreateThread(function()
    while true do
        if not isDrugged then
            -- 按 H 键打开搬运菜单
            if IsControlJustPressed(0, 74) then -- H键
                ShowCarryMenu()
            end
        end
        Wait(0)
    end
end)

-- 导出函数
exports('UseDrugItem', UseDrugItem)
exports('IsDrugged', function() return isDrugged end)
exports('IsCarrying', function() return isCarrying end)
exports('ShowCarryMenu', ShowCarryMenu)

print('^2[器官交易系统] ^7迷药系统客户端已加载')
