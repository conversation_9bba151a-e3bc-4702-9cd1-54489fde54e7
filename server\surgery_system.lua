-- 解剖系统服务器端

-- 开始解剖手术
RegisterNetEvent('organ_trade:startSurgery', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'perform_surgery') then
        NotifyPlayer(source, '你没有权限进行解剖', 'error')
        return
    end

    -- 检查手术者是否被迷晕
    if exports['qiguan_tudou']:IsPlayerDrugged(source) then
        NotifyPlayer(source, Config.Notifications.user_drugged_cannot_use, 'error')
        return
    end

    -- 检查是否有手术刀
    if not HasPlayerItem(source, Config.Surgery.tool_item, 1) then
        NotifyPlayer(source, '你需要手术刀才能进行解剖', 'error')
        return
    end
    
    -- 检查目标是否被迷晕
    if not exports['qiguan_tudou']:IsPlayerDrugged(targetId) then
        NotifyPlayer(source, '目标必须处于迷晕状态才能进行解剖', 'error')
        return
    end
    
    -- 检查是否在指定地点
    local isNearLocation, location = IsPlayerNearLocation(source, Config.Surgery.locations, 10.0)
    if not isNearLocation then
        NotifyPlayer(source, '你必须在指定地点才能进行解剖', 'error')
        return
    end
    
    -- 检查冷却时间
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    if not CheckSurgeryCooldown(targetIdentifier) then
        NotifyPlayer(source, Config.Notifications.cooldown_active, 'error')
        return
    end
    
    -- 检查是否已经在进行手术
    if surgeryInProgress[targetIdentifier] then
        NotifyPlayer(source, '该玩家正在被解剖', 'error')
        return
    end
    
    -- 设置手术状态
    surgeryInProgress[targetIdentifier] = {
        surgeon = source,
        victim = targetId,
        location = location.name,
        startTime = GetGameTimer()
    }
    
    -- 通知双方
    NotifyPlayer(source, Config.Notifications.surgery_started, 'info')
    NotifyPlayer(targetId, '你感到身体被切开...', 'error')
    
    -- 发送器官选择界面给手术者
    TriggerClientEvent('organ_trade:openOrganSelection', source, targetId)
    
    -- 记录日志
    LogAction('SURGERY_STARTED', source, targetId, location.name)
end)

-- 提取器官
RegisterNetEvent('organ_trade:extractOrgan', function(targetId, organKey)
    local source = source
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    
    -- 验证手术状态
    if not surgeryInProgress[targetIdentifier] or surgeryInProgress[targetIdentifier].surgeon ~= source then
        NotifyPlayer(source, '无效的手术状态', 'error')
        return
    end
    
    -- 验证器官选择
    local organ = Config.Surgery.organs[organKey]
    if not organ then
        NotifyPlayer(source, '无效的器官选择', 'error')
        return
    end
    
    -- 检查玩家是否还有该器官
    local playerOrgans = GetPlayerOrgans(targetIdentifier)
    local organField = organ.item
    
    if playerOrgans[organField] == 0 then
        NotifyPlayer(source, '该器官已经被移除', 'error')
        return
    end
    
    -- 开始提取过程
    TriggerClientEvent('organ_trade:startExtraction', source, Config.Surgery.surgery_time)
    
    CreateThread(function()
        Wait(Config.Surgery.surgery_time)
        
        -- 再次验证状态
        if not surgeryInProgress[targetIdentifier] then
            return
        end
        
        -- 给手术者器官道具
        GivePlayerItem(source, organ.item, 1, {
            quality = math.random(70, 100),
            victim = GetPlayerName(targetId),
            extracted_time = os.date('%Y-%m-%d %H:%M:%S')
        })
        
        -- 更新受害者器官状态（同步等待完成）
        local updateSuccess = UpdatePlayerOrgan(targetIdentifier, organField, 0)

        if updateSuccess then
            -- 获取更新后的器官状态
            local updatedOrgans = GetPlayerOrgans(targetIdentifier)

            -- 验证器官状态是否正确更新
            if updatedOrgans[organField] ~= 0 then
                -- 强制设置正确的状态
                updatedOrgans[organField] = 0
            end

            -- 只发送器官状态字段，过滤掉数据库元数据
            local organStatusOnly = {
                organ_heart = updatedOrgans.organ_heart,
                organ_liver = updatedOrgans.organ_liver,
                organ_kidney = updatedOrgans.organ_kidney,
                organ_lung = updatedOrgans.organ_lung,
                organ_pancreas = updatedOrgans.organ_pancreas,
                organ_spleen = updatedOrgans.organ_spleen
            }

            TriggerClientEvent('organ_trade:refreshOrganUI', source, organStatusOnly)
        end

        -- 记录交易
        LogOrganTrade(targetIdentifier, GetPlayerIdentifierByServerId(source), organ.item, surgeryInProgress[targetIdentifier].location)

        -- 设置冷却时间
        SetSurgeryCooldown(targetIdentifier)

        -- 通知
        NotifyPlayer(source, string.format(Config.Notifications.organ_extracted, organ.name), 'success')
        NotifyPlayer(targetId, string.format(Config.Notifications.organ_removed_victim, organ.name), 'error')

        -- 清理手术状态
        surgeryInProgress[targetIdentifier] = nil

        -- 触发受害者的求救系统
        TriggerClientEvent('organ_trade:enableSOS', targetId)

        -- 记录日志
        LogAction('ORGAN_EXTRACTED', source, targetId, string.format('%s (%s)', organ.name, organ.item))
    end)
end)

-- 取消手术
RegisterNetEvent('organ_trade:cancelSurgery', function(targetId)
    local source = source
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    
    if surgeryInProgress[targetIdentifier] and surgeryInProgress[targetIdentifier].surgeon == source then
        surgeryInProgress[targetIdentifier] = nil
        NotifyPlayer(source, '手术已取消', 'info')
        
        LogAction('SURGERY_CANCELLED', source, targetId, 'Surgery cancelled by surgeon')
    end
end)

-- 获取附近被迷晕的玩家
RegisterNetEvent('organ_trade:getNearbyDruggedPlayers', function()
    local source = source
    local sourceCoords = GetPlayerCoords(source)
    local nearbyPlayers = {}
    
    local allPlayers = GetOnlinePlayersExcept(source)
    
    for _, player in ipairs(allPlayers) do
        -- 检查是否被迷晕
        if exports['qiguan_tudou']:IsPlayerDrugged(player.id) then
            local targetCoords = GetPlayerCoords(player.id)
            local distance = GetDistance(sourceCoords, targetCoords)
            
            if distance <= 5.0 then -- 5米范围内
                table.insert(nearbyPlayers, {
                    id = player.id,
                    name = player.name,
                    distance = math.floor(distance * 100) / 100
                })
            end
        end
    end
    
    TriggerClientEvent('organ_trade:receiveNearbyDruggedPlayers', source, nearbyPlayers)
end)

-- 检查手术状态
RegisterNetEvent('organ_trade:checkSurgeryStatus', function()
    local source = source
    local identifier = GetPlayerIdentifierByServerId(source)
    
    local isBeingSurgery = surgeryInProgress[identifier] ~= nil
    TriggerClientEvent('organ_trade:surgeryStatusResponse', source, isBeingSurgery)
end)

-- 获取玩家器官状态
RegisterNetEvent('organ_trade:getPlayerOrganStatus', function(targetId)
    local source = source

    if not HasPermission(source, 'perform_surgery') then
        return
    end

    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    local organs = GetPlayerOrgans(targetIdentifier)

    -- 只发送器官状态字段，过滤掉数据库元数据
    local organStatusOnly = {
        organ_heart = organs.organ_heart,
        organ_liver = organs.organ_liver,
        organ_kidney = organs.organ_kidney,
        organ_lung = organs.organ_lung,
        organ_pancreas = organs.organ_pancreas,
        organ_spleen = organs.organ_spleen
    }

    TriggerClientEvent('organ_trade:receiveOrganStatus', source, organStatusOnly)
end)

-- 导出函数
exports('IsPlayerInSurgery', function(playerId)
    local identifier = GetPlayerIdentifierByServerId(playerId)
    return surgeryInProgress[identifier] ~= nil
end)

print('^2[器官交易系统] ^7解剖系统模块已加载')
