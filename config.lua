Config = {}

-- 基础配置
Config.Locale = 'zh'
Config.Debug = false

-- 迷药系统配置
Config.Drug = {
    item = 'drug_knockout',           -- 迷药道具名称
    duration = 30000,                 -- 迷晕持续时间(毫秒)
    effect_delay = 5000,              -- 药效发作延迟(毫秒)
    use_distance = 3.0               -- 使用距离
}

-- 解剖系统配置
Config.Surgery = {
    tool_item = 'surgery_knife',      -- 手术刀道具名称
    locations = {                     -- 解剖地点
        {x = 123.45, y = -678.90, z = 28.30, name = '废弃仓库'},
        {x = 234.56, y = -789.01, z = 35.40, name = '地下室'}
    },
    organs = {                        -- 器官配置
        A = {name = '心脏', item = 'organ_heart', price = 50000},
        B = {name = '肝脏', item = 'organ_liver', price = 30000},
        C = {name = '肾脏', item = 'organ_kidney', price = 25000},
        D = {name = '肺部', item = 'organ_lung', price = 20000},
        F = {name = '胰腺', item = 'organ_pancreas', price = 18000},
        G = {name = '脾脏', item = 'organ_spleen', price = 12000}
    },
    cooldown = 1000,              -- 解剖冷却时间(1小时)
    surgery_time = 10000             -- 解剖时间(毫秒) - 减少到10秒以改善体验
}

-- 求救系统配置
Config.Rescue = {
    command = 'sos',                 -- 求救命令
    response_time = 300000,          -- 医护响应时间(5分钟)
    jobs = {'ambulance', 'doctor'},  -- 可响应的职业
    blip = {
        sprite = 61,
        color = 1,
        scale = 1.0
    }
}

-- 医疗系统配置
Config.Medical = {
    adrenaline_item = 'adrenaline',  -- 肾上腺素道具
    extend_time = 600000,            -- 延长生命时间(10分钟)
    hospital_locations = {           -- 医院位置
        {x = 298.67, y = -584.23, z = 43.26, name = '中央医院'},
        {x = 1839.6, y = 3672.93, z = 34.28, name = '沙漠医院'}
    },
    surgery_table = {                -- 手术台位置
        {x = 302.45, y = -582.11, z = 43.26},
        {x = 1841.2, y = 3670.45, z = 34.28}
    },
    repair_cost = {                  -- 器官修复费用
        organ_heart = 100000,
        organ_liver = 60000,
        organ_kidney = 50000,
        organ_lung = 40000,
        organ_pancreas = 36000,
        organ_spleen = 24000
    }
}

-- 通知配置
Config.Notifications = {
    drug_used = '你使用了迷药',
    drug_effect = '药效开始发作...',
    victim_drugged = '你感到头晕目眩，身体失去控制...',
    victim_collapsed = '你倒在了地上，只能缓慢爬行...',
    crawling_hint = '按住移动键可以缓慢爬行',
    target_already_drugged = '该玩家已被迷晕，无法重复使用迷药',
    user_drugged_cannot_use = '你处于迷晕状态，无法使用迷药',
    surgery_started = '开始解剖手术',
    organ_extracted = '成功摘除%s',
    organ_removed_victim = '你的%s被摘除了，你感到身体不适...',
    sos_sent = '求救信号已发送',
    medic_notified = '医护人员已收到求救信号',
    adrenaline_used = '使用肾上腺素延长生命',
    surgery_completed = '手术治疗完成',
    organ_repaired = '器官修复完成: %s',
    cooldown_active = '该玩家在冷却时间内，无法进行解剖',
    player_moved = '已将玩家移动到指定位置',
    being_carried = '你被扛在肩上...',
    carry_started = '开始扛肩',
    carry_stopped = '停止扛肩'
}

-- 权限配置
Config.Permissions = {
    use_drug = {},                   -- 可使用迷药的职业/帮派 (空表示所有玩家)
    perform_surgery = {},            -- 可进行解剖的职业/帮派 (空表示所有玩家)
    medical_response = {'ambulance', 'doctor'} -- 医疗响应职业
}
