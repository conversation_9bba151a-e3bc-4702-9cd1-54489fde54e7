-- 迷药系统服务器端

-- 使用迷药
RegisterNetEvent('organ_trade:useDrug', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'use_drug') then
        NotifyPlayer(source, '你没有权限使用迷药', 'error')
        return
    end

    -- 检查使用者是否被迷晕
    if IsPlayerDrugged(source) then
        NotifyPlayer(source, Config.Notifications.user_drugged_cannot_use, 'error')
        return
    end

    -- 检查是否有迷药道具
    if not HasPlayerItem(source, Config.Drug.item, 1) then
        NotifyPlayer(source, '你没有迷药', 'error')
        return
    end
    
    -- 检查距离
    local sourceCoords = GetPlayerCoords(source)
    local targetCoords = GetPlayerCoords(targetId)
    local distance = GetDistance(sourceCoords, targetCoords)
    
    if distance > Config.Drug.use_distance then
        NotifyPlayer(source, '距离太远，无法使用迷药', 'error')
        return
    end
    
    -- 检查目标是否已经被迷晕
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    if activeDrugs[targetIdentifier] then
        NotifyPlayer(source, Config.Notifications.target_already_drugged, 'error')
        return
    end

    -- 移除迷药道具
    RemovePlayerItem(source, Config.Drug.item, 1)

    -- 立即设置临时状态，防止重复使用
    activeDrugs[targetIdentifier] = {
        playerId = targetId,
        startTime = GetGameTimer(),
        duration = Config.Drug.duration,
        pending = true -- 标记为等待药效发作
    }

    -- 通知使用者
    NotifyPlayer(source, Config.Notifications.drug_used, 'success')

    -- 延迟药效发作
    CreateThread(function()
        Wait(Config.Drug.effect_delay)

        -- 检查目标是否还在线
        if not ESX.GetPlayerFromId(targetId) then
            -- 如果玩家离线，清除状态
            if activeDrugs[targetIdentifier] then
                activeDrugs[targetIdentifier] = nil
            end
            return
        end

        -- 应用迷药效果
        ApplyDrugEffect(targetId)

        -- 记录日志
        LogAction('DRUG_USED', source, targetId, 'Drug applied successfully')
    end)
end)

-- 应用迷药效果
function ApplyDrugEffect(targetId)
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)

    -- 更新迷药状态，移除pending标记
    activeDrugs[targetIdentifier] = {
        playerId = targetId,
        startTime = GetGameTimer(),
        duration = Config.Drug.duration,
        pending = false -- 药效已发作
    }

    -- 通知目标玩家
    NotifyPlayer(targetId, Config.Notifications.victim_drugged, 'error')
    TriggerClientEvent('organ_trade:applyDrugEffect', targetId, Config.Drug.duration)

    -- 设置定时器清除效果
    CreateThread(function()
        Wait(Config.Drug.duration)

        if activeDrugs[targetIdentifier] then
            activeDrugs[targetIdentifier] = nil
            TriggerClientEvent('organ_trade:removeDrugEffect', targetId)
        end
    end)
end

-- 检查玩家是否被迷晕
function IsPlayerDrugged(playerId)
    local identifier = GetPlayerIdentifierByServerId(playerId)
    return activeDrugs[identifier] ~= nil
end

-- 获取附近的玩家
RegisterNetEvent('organ_trade:getNearbyPlayers', function()
    local source = source
    local sourceCoords = GetPlayerCoords(source)
    local nearbyPlayers = {}

    local allPlayers = GetOnlinePlayersExcept(source)

    for _, player in ipairs(allPlayers) do
        local targetCoords = GetPlayerCoords(player.id)
        local distance = GetDistance(sourceCoords, targetCoords)

        if distance <= Config.Drug.use_distance then
            -- 检查玩家是否已被迷晕
            local isDrugged = IsPlayerDrugged(player.id)

            table.insert(nearbyPlayers, {
                id = player.id,
                name = player.name,
                distance = math.floor(distance * 100) / 100,
                isDrugged = isDrugged
            })
        end
    end

    TriggerClientEvent('organ_trade:receiveNearbyPlayers', source, nearbyPlayers)
end)

-- 强制移动被迷晕的玩家
RegisterNetEvent('organ_trade:movePlayer', function(targetId, coords)
    local source = source
    
    -- 检查权限
    if not HasPermission(source, 'use_drug') then
        return
    end
    
    -- 检查目标是否被迷晕
    if not IsPlayerDrugged(targetId) then
        NotifyPlayer(source, '目标没有被迷晕', 'error')
        return
    end
    
    -- 传送玩家
    TriggerClientEvent('organ_trade:teleportPlayer', targetId, coords)
    
    -- 记录日志
    LogAction('PLAYER_MOVED', source, targetId, string.format('Moved to: %.2f, %.2f, %.2f', coords.x, coords.y, coords.z))
end)

-- 检查迷药状态
RegisterNetEvent('organ_trade:checkDrugStatus', function()
    local source = source
    local identifier = GetPlayerIdentifierByServerId(source)
    local isDrugged = activeDrugs[identifier] ~= nil
    
    TriggerClientEvent('organ_trade:drugStatusResponse', source, isDrugged)
end)

-- 导出函数
exports('IsPlayerDrugged', IsPlayerDrugged)
exports('ApplyDrugEffect', ApplyDrugEffect)

-- 注册迷药道具使用
ESX.RegisterUsableItem(Config.Drug.item, function(source)
    local xPlayer = ESX.GetPlayerFromId(source)

    -- 检查权限
    if not HasPermission(source, 'use_drug') then
        NotifyPlayer(source, '你没有权限使用迷药', 'error')
        return
    end

    -- 检查使用者是否被迷晕
    if IsPlayerDrugged(source) then
        NotifyPlayer(source, Config.Notifications.user_drugged_cannot_use, 'error')
        return
    end

    -- 触发客户端选择目标
    TriggerClientEvent('organ_trade:startDrugSelection', source)
end)

print('^2[器官交易系统] ^7迷药系统模块已加载')
